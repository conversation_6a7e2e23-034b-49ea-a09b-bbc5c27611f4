import { FILE_CONFIG, ERROR_MESSAGES } from "../constants";

const KILOBYTE = 1024;
const FILE_SIZE_UNITS = ["Bytes", "KB", "MB", "GB"];

export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return "0 Bytes";

  const i = Math.floor(Math.log(bytes) / Math.log(KILOBYTE));
  return parseFloat((bytes / Math.pow(KILOBYTE, i)).toFixed(2)) + " " + FILE_SIZE_UNITS[i];
};

export const validateEDFFile = (file: File): { valid: boolean; error?: string } => {
  const fileName = file.name.toLowerCase();
  const hasValidExtension = FILE_CONFIG.ALLOWED_EXTENSIONS.some((ext) => fileName.endsWith(ext.toLowerCase()));

  if (!hasValidExtension) {
    return { valid: false, error: ERROR_MESSAGES.INVALID_FILE_TYPE };
  }

  if (file.size > FILE_CONFIG.MAX_SIZE_BYTES) {
    return { valid: false, error: ERROR_MESSAGES.FILE_TOO_LARGE };
  }

  if (file.size === 0) {
    return { valid: false, error: "File cannot be empty" };
  }

  return { valid: true };
};

export const formatDate = (dateString: string): string => {
  const date = new Date(dateString);
  return date.toLocaleString("en-US", {
    year: "numeric",
    month: "short",
    day: "numeric",
    hour: "2-digit",
    minute: "2-digit",
  });
};
